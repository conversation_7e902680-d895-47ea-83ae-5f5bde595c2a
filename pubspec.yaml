name: agriculture
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  animate_do: ^4.2.0
  buttons_tabbar: ^1.3.15
  cached_network_image: ^3.4.1
  camera: ^0.10.5+9
  carousel_slider: ^5.0.0
  chewie: ^1.11.3
  cloud_firestore: ^5.6.7
  connectivity_plus: ^6.1.4
  cross_file: ^0.3.4+2
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  equatable: ^2.0.7
  firebase_auth: ^5.5.3
  firebase_core: ^3.13.0
  firebase_storage: ^12.4.5
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_conditional_rendering: ^2.1.0
  flutter_image_compress: ^2.4.0
  flutter_localizations:
    sdk: flutter
  flutter_markdown: ^0.6.18
  flutter_native_splash: ^2.4.6
  flutter_riverpod: ^2.6.1
  flutter_svg: ^2.1.0
  geolocator: ^14.0.0
  get_it: ^8.0.3
  google_maps_flutter: ^2.12.3
  google_sign_in: ^6.3.0
  googleapis: ^14.0.0
  googleapis_auth: ^2.0.0
  http: ^1.4.0
  image: ^4.3.0
  image_picker: ^1.1.2
  audioplayers: ^8.0.0
  jiffy: ^6.3.2
  list_wheel_scroll_view_nls: ^0.0.3
  loading_animation_widget: ^1.3.0
  lottie: ^3.3.1
  path: ^1.9.0
  path_provider: ^2.1.5
  photo_view: ^0.15.0
  retry: ^3.1.2
  riverpod: ^2.6.1
  share_plus: ^11.0.0
  shared_preferences: ^2.5.3
  shimmer: ^3.0.0
  smooth_page_indicator: ^1.2.1
  sqflite: ^2.4.1
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  video_player: ^2.9.5
  weather_icons: ^3.0.0
  youtube_player_flutter: ^9.0.1
dev_dependencies:
  bloc_test: ^10.0.0
  build_runner: ^2.4.8

  flutter_lints: ^5.0.0

  flutter_localization: ^0.3.2
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4

flutter_native_splash:
  image: assets/images/splash/play1.gif
  color: "#00bfa5"

flutter:

  uses-material-design: true
  assets:
    - assets/
    - assets/fonts/
    - assets/fonts/cairo/
    - assets/fonts/sultan/
    - assets/fonts/eimessiri/
    - assets/images/
    - assets/images/slider/
    - assets/images/splash/
    - assets/images/phone_otp/
    - assets/images/login_image/
    - assets/images/wether_icon/
    - assets/images/register_image/
    - assets/iconLogin/
    - assets/images/on_boarding/
    - assets/images/on_boarding/image1.png
    - assets/db/agricluture_DB.db
    - assets/wether_svg/
    - assets/wether_svg/static/
    - assets/wether_svg/animated/
    - assets/icons/
    - assets/config/
    - assets/credentials/

  fonts:
    - family: sultan
      fonts:
        - asset: assets/fonts/sultan/sultan.ttf
    #       - asset: fonts/Schyler-Italic.ttf
    #         style: italic
    - family: cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
          weight: 700

    - family: cairo1
      fonts:
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
    - family: ElMessiri
      fonts:
        - asset: assets/fonts/eimessiri/ElMessiri-Bold-3.ttf
